<!-- 自定义二次确认是否审过申报弹框 -->
<template>
    <el-dialog top="6%" width="60%" title="检查项目详情" :close-on-click-modal="false" :visible.sync="currentIsShow">
        <div class="detail-container">
            <el-row>
                <el-col :span="24">
                    <table style="width:100%;border:1px solid #ddd;" class="table-container">
                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                项目名称
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.projectName }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4">
                                项目编号
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.projectNumber }}</span>
                            </th>
                        </tr>

                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                结果类型
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.resultTypeName }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4">
                                性别限制
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.genderLimitName }}</span>
                            </th>
                        </tr>

                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                年龄限制
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.ageLimitRange }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4">
                                婚姻状况限制
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.marriageName }}</span>
                            </th>
                        </tr>

                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                参考范围
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.standardRange }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4">
                                极值范围
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.extremeRange }}</span>
                            </th>
                        </tr>

                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                单位
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.unitName }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4">
                                适用类型
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.suitTypeName }}</span>
                            </th>
                        </tr>

                        <tr>
                            <th class="left-tittle table-label" colspan="4">
                                创建时间
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.createTimeFormat }}</span>
                            </th>
                            <th class="left-tittle table-label" colspan="4">
                                更新时间
                            </th>
                            <th colspan="8" class="th-input">
                                <span>{{ itemData.createTimeFormat }}</span>
                            </th>
                        </tr>
                    </table>
                </el-col>
            </el-row>
        </div>
        <div class="el-grant-the-staff-foot footer-distance">
            <el-button @click="close" size="middle" type="primary">关闭</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    name: 'itemDetail',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        itemData: {
            default: () => {
                return {}
            }
        }
    },
    computed: {
        currentIsShow: {
            get() {
                return this.show
            },
            set(val) {
                this.$emit('update:show', val)
            }
        }
    },
    methods: {
        close() {
            this.currentIsShow = false
            this.$emit('resetData')    // 清除数据缓存
        }
    },
    filters: {
        handletoPhone(val) {
            let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
            let phone = val.replace(reg, '$1****$2');
            return phone;
        },
        handletoIdNumber(val) {
            const reg = /^(\d{6})\d{8}(\d{4})$/;
            return val.replace(reg, "$1********$2");
        }
    }
}
</script>

<style scoped lang="scss">
.detail-container {
    padding: 0px 0px;

    .table-container {
        table-layout: fixed;
        background: #fff;
        width: 100%;
        margin: 0 auto;
        border-collapse: collapse;

        td {
            padding: 5px 0;
        }

        th {
            font-weight: normal;
            border: 1px solid #e5e5e5;
            padding: 3px 0;
        }

        tr {
            border: 1px solid #e5e5e5;
            width: 100%;
            font-size: 14px;
        }

        .left-tittle {
            background: #ECF5FF;
        }

        .center-tittle {
            background: #ECF5FF;
        }

        .table-label {
            text-align: right;
            padding-right: 5px;
            color: #000;
            font-size: 14px;
            height: 42px;
        }

        .th-input {
            text-align: center;
            padding: 2px 4px;

            span {
                color: #333;
            }
        }

        .th-radio {
            text-align: center;
            padding-left: 6px;
            padding-right: 6px;
        }

        .input-width {
            width: 200px;
            background-color: bisque;
        }

        .health-check-th {
            padding: 11px 28px;
            text-align: center;
        }
    }
}

.confirm-handler {
    padding: 5px 5px 5px 36px;
}

.footer-distance {
    text-align: right;
    margin-top: 15px !important;
}

::v-deep .el-dialog__body {
    padding: 0px 20px 30px 20px;
}
</style>