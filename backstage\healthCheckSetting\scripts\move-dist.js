const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取项目名称（小驼峰格式）
const projectName = process.env.npm_package_name;
const camelCaseName = projectName.replace(/-([a-z])/g, g => g[1].toUpperCase());

// 定义源目录和目标目录
const sourceDir = path.join(__dirname, '../dist');
const targetDir = path.join(__dirname, '../../dist', camelCaseName);

// 删除目标目录（如果存在）
function removeTargetDir() {
  if (fs.existsSync(targetDir)) {
    if (process.platform === 'win32') {
      // Windows 系统使用 rmdir /s /q
      execSync(`rmdir /s /q "${targetDir}"`, { stdio: 'inherit' });
    } else {
      // Mac/Linux 系统使用 rm -rf
      execSync(`rm -rf "${targetDir}"`, { stdio: 'inherit' });
    }
  }
}

// 移动文件
function moveFiles() {
  try {
    // 确保目标目录存在
    if (!fs.existsSync(path.join(__dirname, '../../dist'))) {
      fs.mkdirSync(path.join(__dirname, '../../dist'), { recursive: true });
    }

    // 移动文件
    if (process.platform === 'win32') {
      // Windows 系统使用 xcopy
      execSync(`xcopy /E /I /Y "${sourceDir}" "${targetDir}"`, { stdio: 'inherit' });
    } else {
      // Mac/Linux 系统使用 cp -r
      execSync(`cp -r "${sourceDir}" "${targetDir}"`, { stdio: 'inherit' });
    }

    console.log(`✅ 成功将构建文件移动到 ${targetDir}`);
  } catch (error) {
    console.error('❌ 移动文件时发生错误:', error);
    process.exit(1);
  }
}

// 执行移动操作
removeTargetDir();
moveFiles();
