<template>
    <el-dialog top="3%" width="60%" title="更改检查项目" :close-on-click-modal="false" :visible.sync="currentIsShow">
        <div class="detail-container">
            <el-form :model="editData" :rules="rules" ref="ruleForm" label-width="160px" class="demo-ruleForm">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="项目名称" prop="projectName">
                            <el-input v-model="editData.projectName" style="width:200px;"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="项目编号" prop="projectNumber">
                            <el-input v-model="editData.projectNumber" style="width:200px;"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="结果类型" prop="resultType">
                            <el-select v-model="editData.resultType" placeholder="请选择" style="width:200px;">
                                <el-option :label="item.name" :value="item.value" v-for="item in resultTypeOptions"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="适用类型" prop="suitType">
                            <el-select v-model="editData.suitType" placeholder="请选择适用类型" style="width:200px;" multiple>
                                <el-option label="职业健康检查项目" :value="1"></el-option>
                                <el-option label="平时体检项目" :value="2"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="婚姻状况限制" prop="marriage">
                            <el-select v-model="editData.marriage" placeholder="请选择婚姻限制" style="width:200px;">
                                <el-option label="不限" :value="1"></el-option>
                                <el-option label="未婚" :value="2"></el-option>
                                <el-option label="已婚" :value="3"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="性别限制" prop="genderLimit">
                            <el-select v-model="editData.genderLimit" placeholder="请选择性别限制" style="width:200px;">
                                <el-option label="不限" :value="1"></el-option>
                                <el-option label="男性" :value="2"></el-option>
                                <el-option label="女性" :value="3"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="计量单位" prop="msrunt">
                            <el-select v-model="editData.msrunt" placeholder="请选择"  filterable allow-create default-first-option style="width:200px;">
                                <el-option :label="item.name" :value="item.value" v-for="item in unitOption"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>


                <el-row>
                    <el-col :span="12">
                        <el-form-item label="是否有限制年龄" prop="isLimitAge">
                            <el-radio-group v-model="editData.isLimitAge">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="2">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row v-show="editData.isLimitAge == 1">
                    <el-col :span="12">
                        <el-form-item label="最小年龄限制" prop="ageLimitMin">
                            <el-input-number style="width:200px;" v-model="editData.ageLimitMin" @change="handleChangeMin" :min="1" :max="200" label="最小年龄"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="最大年龄限制" prop="ageLimitMax">
                            <el-input-number style="width:200px;" v-model="editData.ageLimitMax" @change="handleChangeMax" :min="1" :max="200" label="最大年龄"></el-input-number>
                        </el-form-item>
                    </el-col>
                </el-row>


                <el-row>
                    <el-col :span="12">
                        <el-form-item label="是否有参考范围" prop="isStandard">
                            <el-radio-group v-model="editData.isStandard">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="2">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row v-show="editData.isStandard == 1">
                    <el-col :span="12">
                        <el-form-item label="参考范围下限" prop="standardValueMin">
                            <el-input v-model="editData.standardValueMin" style="width:200px;" placeholder="参考范围最小值"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="参考范围上限" prop="standardValueMax">
                            <el-input v-model="editData.standardValueMax" style="width:200px;" placeholder="参考范围最大值"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="12">
                        <el-form-item label="是否有极值范围" prop="isExtremeValue">
                            <el-radio-group v-model="editData.isExtremeValue">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="2">否</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row v-show="editData.isExtremeValue == 1">
                    <el-col :span="12">
                        <el-form-item label="最小极值" prop="extremeValueMin">
                            <el-input v-model="editData.extremeValueMin" style="width:200px;" placeholder="最小极值"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="最大极值" prop="extremeValueMax">
                            <el-input v-model="editData.extremeValueMax" style="width:200px;" placeholder="最大极值"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row>
                    <el-col :span="24" style="text-align: right;">
                        <el-form-item>
                            <el-button @click="close('ruleForm')" size="middle">关闭</el-button>
                            <el-button @click="confirm('ruleForm')" size="middle" type="primary">确认更改</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
        <!-- <div class="el-grant-the-staff-foot footer-distance">
            <el-button @click="close" size="middle">关闭</el-button>
            <el-button @click="confirm" size="middle" type="primary">确认更改</el-button>
        </div> -->
    </el-dialog>
</template>

<script>
export default {
    name: 'AddNewHealthCheck',
    props: {
        show: {
            type: Boolean,
            default: false
        },
        titleName: {
            type: String,
            default: () => {
                return ''
            }
        },
        editData: {
            default: () => {
                return {}
            }
        },
        resultTypeOptions:{

        },
        unitOption:{

        }
    },
    computed: {
        currentIsShow: {
            get() {
                return this.show
            },
            set(val) {
                this.$emit('update:show', val)
            }
        }
    },
    watch:{
        'editData.isStandard'(val){
            if(val === 2){
                // 无参考范围，则清除之前的值
                this.editData.standardValueMin = ''
                this.editData.standardValueMax = ''
            }
        },
        'editData.isExtremeValue'(val){
            if(val === 2){
                // 如无极值参考范围，则清除之前的值
                this.editData.extremeValueMin = ''
                this.editData.extremeValueMax = ''
            }
        },
        'editData.isLimitAge'(val){
            if(val === 2){
                // 如无极值参考范围，则清除之前的值
                this.editData.ageLimitMin = ''
                this.editData.ageLimitMax = ''
            }
        }
    },
    data(){
        return {
            editData:{
                projectName:'',
                projectNumber:'',
                marriage:'',
                genderLimit:'',
                isLimitAge:null,
                ageLimitMin:'',
                ageLimitMax:'',
                resultType:'',
                msrunt:'',
                isStandard:'',
                standardValueMin:null,
                standardValueMax:null,
                isExtremeValue:null,
                extremeValueMin:'',
                extremeValueMax:''
            },
            rules: {
                projectName: [
                    { required: true, message: '请输入检查项目名称', trigger: 'blur' },
                ],
                projectNumber: [
                    { required: true, message: '请输入检查项目编号', trigger: 'blur' },
                ],
                resultType:[
                    { required: true, message: '请输入结果类型', trigger: 'blur' },
                ],
                suitType:[
                    { required: true, message: '请选择适用类型', trigger: 'blur' },
                ],
            }
        }
    },
    methods: {
        confirm(formName){
            if(this.editData.isStandard == 1 && (this.editData.standardValueMin === '' || this.editData.standardValueMin === null || this.editData.standardValueMax === '' || this.editData.standardValueMax === null)){
                this.$message.warning('请填写参考值范围。')
                return
            }
            if(this.editData.isExtremeValue == 1 && (this.editData.extremeValueMin === '' || this.editData.extremeValueMin === null || this.editData.extremeValueMax === '' || this.editData.extremeValueMax === null)){
                this.$message.warning('请填写极值范围。')
                return
            }
            if(this.editData.isLimitAge == 1 && (this.editData.ageLimitMin === '' || this.editData.ageLimitMin === null || this.editData.ageLimitMax === '' || this.editData.ageLimitMax === null)){
                this.$message.warning('请填写年龄范围。')
                return
            }
            this.$refs[formName].validate((valid) => {
                const { isLimitAge, isStandard, isExtremeValue } = this.editData     // 对极值/参考值/年龄 进行判断，若无限制，清楚之前已填写的数据避免造成数据污染
                if (isLimitAge == 2) {
                    [this.editData.ageLimitMin, this.editData.ageLimitMax] = ['', '']
                }
                if (isStandard == 2) {
                    [this.editData.standardValueMin, this.editData.standardValueMax] = ['', '']
                }
                if (isExtremeValue == 2) {
                    [this.editData.extremeValueMin, this.editData.extremeValueMax] = ['', '']
                }
                if (valid) {
                    this.currentIsShow = false
                    this.$emit('confirm',this.editData)
                }
            })
        },
        close(formName) {
            this.$refs[formName].resetFields()
            this.currentIsShow = false
        }
    },
    filters: {
        handletoPhone(val) {
            let reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/;
            let phone = val.replace(reg, '$1****$2');
            return phone;
        },
        handletoIdNumber(val) {
            const reg = /^(\d{6})\d{8}(\d{4})$/;
            return val.replace(reg, "$1********$2");
        }
    }
}
</script>

<style scoped lang="scss">
.detail-container {
    padding: 10px 10px;
}

.confirm-handler {
    padding: 5px 5px 5px 36px;
}

.footer-distance {
    text-align: right;
    margin-top: 15px !important;
}

::v-deep .el-dialog__body {
    padding: 0px 20px 30px 20px;
}
</style>