/*
 * @Author: lcz
 * @Description: 体检登记服务
 */

const { Service } = require('egg');
const moment = require('moment');
const { nanoid } = require('nanoid');
const pinyin = require('pinyin');


class HealthCheckRegisterService extends Service {

  // 通过体检编号获取体检登记信息
  async getRegisterDetailByCheckNo(checkNo) {
    const { ctx } = this;
    const register = await ctx.model.HealthCheckRegister.findOne({ checkNo: checkNo })
    return register;
  }

  // 通过身份证获取体检登记信息
  async getRegisterDetailByIdNumber(params) {
    const { ctx } = this;
    const { idNumber, physicalOrgID } = params;
    const register = await ctx.model.HealthCheckRegister
      .find({ idNumber, physicalOrgID, })
      .sort({
        status: 1,
        createdAt: -1
      });
    if (register.length > 0) {
      return register[0];
    } else {
      throw new Error('未找到相关体检登记记录');
    }
  }

  // 创建体检登记(未进行预约直接登记)
  async createRegister(data) {
    const { ctx } = this;
    // 查询是否存在该user，不存在则创建user
    let user = await ctx.model.User.findOne({ phoneNum: phone });
    if (!user) {
      user = await ctx.model.User.create({
        phoneNum: phone,
        name,
        userName: phone,
        idNo: idNumber,
      });
    }
    // 根据身份证查询企业是否存在该员工employee，如果存在则获取employeeId，如果不存在则创建employee
    const { idNumber, name, gender, phone } = data;
    let employee = await ctx.model.Employee.findOne({ IDNum: idNumber, phoneNum: phone });
    if (!employee) {
      employee = await ctx.model.Employee.create({
        name,
        gender: parseInt(gender) - 1 + '',
        phoneNum: phone,
        IDNum: idNumber,
        userId: user._id,
        EnterpriseID: data.EnterpriseID || '',
      });
    }

    // 生成体检编号
    data.checkNo = await this._generateCheckNo(employee._id);
    data.status = 1;
    data.physicalOrgID = ctx.session.physicalExamUserInfo.EnterpriseID;
    // 当天日期
    data.registerTime = new Date(moment().format('YYYY-MM-DD'));
    const res = await ctx.model.HealthCheckRegister.create(data);
    return res
  }

  // 更新体检登记
  async updateRegister(data) {
    const { ctx } = this;
    const { _id } = data;
    data.status = 1;
    data.registerTime = new Date(moment().format('YYYY-MM-DD'));

    // 检查记录是否存在
    const find = await this._findRegisterById(_id);

    if (find.status >= 2) {
      throw new Error('当前体检记录已完成总结，无法修改！');
    }

    // 数据验证
    this._validateRegisterData(data);
    // console.log('🍊 HealthCheckRegister updateRegister data.checkHazardFactors', data.checkHazardFactors);
    this._updateOperator(find.checkNo);

    return await ctx.model.HealthCheckRegister.findByIdAndUpdate(_id, data);
  }

  // 获取体检登记列表
  async getRegisterList(params = {}) {
    const { ctx } = this;
    const {
      pageNum = 1,
      pageSize = 10,
      name,
      checkNo,
      idNumber,
      physicalOrgID,
      checkType,
      examType,
    } = params;

    const filter = {
      physicalOrgID,
      status: 1
    };

    // 条件过滤
    if (name) filter.name = new RegExp(name, 'i');
    if (checkNo) filter.checkNo = new RegExp(checkNo, 'i');
    if (idNumber) filter.idNumber = new RegExp(idNumber, 'i');
    if (checkType) filter.checkType = checkType;
    if (examType) filter.examType = examType;

    const list = await ctx.model.HealthCheckRegister.find(filter)
      .sort({ registerTime: -1 })
      .skip((pageNum - 1) * pageSize)
      .limit(pageSize)
      .populate({ path: 'EnterpriseID', select: 'cname' })
    // .populate('checkProjects');

    const total = await ctx.model.HealthCheckRegister.countDocuments(filter);

    return {
      list,
      total,
      pageNum,
      pageSize
    };
  }

  // 获取体检登记详情
  async getRegisterDetail(_id) {
    const { ctx } = this;
    const register = await ctx.model.HealthCheckRegister.findById(_id).populate({ path: 'EnterpriseID', select: 'cname' });

    if (!register) {
      throw new Error('体检登记记录不存在');
    }

    return register;
  }

  // 删除体检登记
  async deleteRegister(_id) {
    const { ctx } = this;

    // 检查记录是否存在
    await this._findRegisterById(_id);

    return await ctx.model.HealthCheckRegister.findByIdAndDelete(_id);
  }

  // 获取科室和收费项目
  async getDepartmentAndProjectList(paload) {
    const { physicalOrgId } = paload;
    const { ctx } = this;
    // TODO 收费项目年龄、婚姻限制筛选 , gender, Marriage, age
    const res = await ctx.model.HealthDepartment.find({ physicalOrgId })
      .populate('departmentCheckItem')
      .populate('departmentCheckItem.includeProject');
    return res;
  }

  // 获取当日登记人员列表
  async getTodayRegisterList(physicalOrgID) {
    const { ctx } = this;
    const today = moment().format('YYYY-MM-DD');
    const res = await ctx.model.HealthCheckRegister.find({ physicalOrgID, registerTime: { $gte: today } })
      // .populate('employee')
      .populate({ path: 'EnterpriseID', select: 'cname' })
    return res;
  }

  // 结果录入
  async inputResult(data) {
    const { ctx } = this;
    const { _id, departmentId, projectList } = data;

    const doc = await ctx.model.HealthCheckRegister.findById(_id);
    if (!doc) {
      throw new Error('体检登记记录不存在');
    }
    if (doc.status === 2) {
      throw new Error('体检已总结，无法录入结果');
    }
    const department = doc.checkDepartments.find(item => item.departmentId === departmentId);
    if (department.status !== 0) {
      throw new Error('科室已小结，无法录入结果');
    }

    projectList.forEach(async project => {
      const projectId = project._id;
      const checkItems = project.includeProject.map(item => {
        const { _id, result, conclusion, msrunt, standardValueMin, standardValueMax, projectName } = item;
        return {
          itemId: _id,
          result,
          conclusion,
          msrunt,
          standardValueMin,
          standardValueMax,
          itemName: projectName,
        }
      });
      const res = await ctx.model.HealthCheckRegister.updateOne(
        { _id },
        {
          $set: {
            "checkDepartments.$[department].checkProjects.$[project].checkItems": checkItems
          }
        },
        {
          arrayFilters: [
            { "department.departmentId": departmentId }, // 定位科室
            { "project.projectId": projectId }        // 定位项目
          ]
        }
      )
      // console.log('🍊 HealthCheckRegister res ', departmentId, projectId, res);
    });

    this._updateOperator(doc.checkNo);

    return 'ok';
  }

  // 科室小结
  async departmentSummary(data) {
    const { ctx } = this;
    const { _id, departmentId, type, summary } = data;

    const doc = await ctx.model.HealthCheckRegister.findById(_id);
    if (!doc) {
      throw new Error('体检登记记录不存在');
    }
    if (doc.status === 2) {
      throw new Error('体检已总结，无法进行小结');
    }

    if (type == 0) { // 取消小结
      const res = await ctx.model.HealthCheckRegister.updateOne(
        { _id },
        {
          $set: {
            "checkDepartments.$[department].summary": '',
            "checkDepartments.$[department].status": type
          }
        },
        {
          arrayFilters: [
            { "department.departmentId": departmentId } // 定位科室
          ]
        }
      )
      console.log('🍊 HealthCheckRegister departmentSummary ', departmentId, res);
      return 'ok';
    }

    // const department = doc.checkDepartments.find(item => item.departmentId === departmentId);
    // if (department.status !== 0) {
    //   throw new Error('科室已小结，无法重复小结');
    // }

    // 更新科室小结
    const res = await ctx.model.HealthCheckRegister.updateOne(
      { _id },
      {
        $set: {
          "checkDepartments.$[department].summary": summary,
          "checkDepartments.$[department].status": type
        }
      },
      {
        arrayFilters: [
          { "department.departmentId": departmentId } // 定位科室
        ]
      }
    )
    // console.log('🍊 HealthCheckRegister departmentSummary ', departmentId, res);

    this._updateOperator(doc.checkNo);

    return 'ok';

  }

  // 检查总结
  async healthCheckSummary(data) {
    const { ctx } = this;
    const { _id, healthSummary, suggestion, jobSummary, recheckTime, recheckProjects, checkHazardFactors } = data;
    const doc = await ctx.model.HealthCheckRegister.findById(_id);
    const setData = {
      healthSummary,
      suggestion,
      jobSummary,
      recheckTime,
      recheckProjects,
      checkHazardFactors,
      // jobConclusion,
      status: 2
    }
    if (!doc) {
      throw new Error('体检登记记录不存在');
    }
    if (doc.status === 2) {
      throw new Error('体检已总结，无法重复总结！');
    }
    // 根据危害因素结论获取职检结论
    let jobConclusion = []
    // 获取各危害因素职检结论
    // 1  目前未见异常
    // 2  复查
    // 3  疑似职业病
    // 4  禁忌证
    // 5  其他疾病或异常
    checkHazardFactors.forEach(item => {
      item.conclusion && item.conclusion.length > 0 && jobConclusion.push(...item.conclusion)
    })
    // 去重
    jobConclusion = [...new Set(jobConclusion)]
    // 如果有除了未见异常外的结论，则结论中不包含未见异常
    if (jobConclusion.length > 1 && jobConclusion.includes(1)) {
      jobConclusion = jobConclusion.filter(item => item !== 1);
    }

    if (Array.isArray(jobConclusion) && jobConclusion.some(item => item === 3)) {
      setData.reportStatus = 1;
    }

    setData.jobConclusion = jobConclusion;

    // 更新检查总结
    const res = await ctx.model.HealthCheckRegister.updateOne(
      { _id },
      {
        $set: setData
      })
    // console.log('🍊 HealthCheckRegister healthCheckSummary ', res);
    ctx.auditLog('上传体检检查总结', `HealthCheckRegister._id: ${_id}; ${res}`, 'info');

    this._updateOperator(doc.checkNo);

    return 'ok';
  }

  // 获取体检报告列表
  async getHCReportList(params = {}) {
    const { ctx } = this;
    const {
      physicalOrgID,
      pageNum = 1,
      pageSize = 10,
      name, // 姓名
      idNumber, // 身份证号
      phone,  // 手机号
      examType, // 体检类别
      // orgName, // 机构名称
    } = params;

    const filter = {
      physicalOrgID,
      status: { $gte: 2 } // 已总结
    };

    // 条件过滤
    if (name) filter.name = new RegExp(name, 'i');
    if (phone) filter.phone = new RegExp(phone, 'i');
    if (idNumber) filter.idNumber = new RegExp(idNumber, 'i');
    if (examType) filter.examType = examType;
    // if (orgName) filter.orgName = new RegExp(orgName, 'i');
    const list = await ctx.model.HealthCheckRegister.aggregate([
      { $match: { ...filter } },
      { $sort: { registerTime: -1 } },
      {
        $lookup: {
          from: "adminorgs",
          localField: "EnterpriseID",
          foreignField: "_id",
          as: "Enterprise"
        }
      },
      {
        $lookup: {
          from: "physicalExamOrgs",
          localField: "physicalOrgID",
          foreignField: "_id",
          as: "physicalOrg"
        }
      },
      // {
      //   $group: {
      //     _id: {
      //       idNumber: "$idNumber"
      //     },
      //     allReport: {
      //       $push: "$$ROOT"
      //     },
      //     total: {
      //       $sum: 1
      //     },
      //     name: {
      //       $first: "$name"
      //     },
      //     phone: {
      //       $first: "$phone"
      //     },
      //     idNumber: {
      //       $first: "$idNumber"
      //     },
      //     examType: {
      //       $first: "$examType"
      //     },
      //     registerTime: {
      //       $first: "$registerTime"
      //     },
      //     physicalOrgName: {
      //       $first: "$physicalOrg.name"
      //     },
      //     id: {
      //       $first: "$_id"
      //     },
      //     status: {
      //       $first: "$status"
      //     }
      //   }
      // },
      { $skip: (pageNum - 1) * pageSize },
      { $limit: pageSize },
      {
        $project: {
          // _id: 0,
          // allReport: {
          //   _id: 1,
          //   registerTime: 1,
          //   checkNo: 1
          // },
          total: 1,
          name: 1,
          phone: 1,
          idNumber: 1,
          examType: 1,
          registerTime: 1,
          physicalOrgName: "$physicalOrg.name",
          id: '$_id',
          status: 1
        }
      },

    ]);

    const total = await ctx.model.HealthCheckRegister.countDocuments(filter);
    return {
      list,
      total,
    };
  }

  // 报告审核
  async reportReview(data) {
    const { ctx } = this;
    const { _id, auditReason, status } = data;
    const doc = await ctx.model.HealthCheckRegister.findById(_id);
    if (!doc) {
      throw new Error('体检登记记录不存在');
    }
    if (doc.status < 2) {
      throw new Error('体检未总结，无法审核');
    }
    if (doc.status > 2) {
      throw new Error('体检已审核，无法重复审核');
    }
    const res = await ctx.model.HealthCheckRegister.findByIdAndUpdate(_id, { auditReason, status });
    ctx.auditLog('体检报告审核', `HealthCheckRegister._id: ${_id}; ${res}`, 'info');
    if (status !== 3) {
      return res;
    }
    const PhysicalExamOrg = await ctx.model.PhysicalExamOrg.findById(doc.physicalOrgID);
    const age = moment().diff(doc.birthDate, 'years');
    const gender = +doc.gender - 1 + '';
    const workYears = doc.totalWorkYears + '年' + doc.totalWorkMonths + '月';
    const harmFactors = doc.hazardFactors.join('；');
    // 1: 岗前, 2: 在岗, 3: 离岗, 4: 离岗后, 5: 应急 => 0上岗前 1在岗 2离岗时 3复查 4应急 5 离岗后 6 普通体检
    let checkType = 0;
    switch (doc.examType) {
      case 1:
        checkType = 0;
        break;
      case 2:
        checkType = 1;
        break;
      case 3:
        checkType = 2;
        break;
      case 4:
        checkType = 3;
        break;
    }
    const bhkSubList = [];
    const departmentList = [];
    doc.checkDepartments.forEach(department => {
      const departmentName = department.departmentName;
      const summary = department.summary;
      department.checkProjects.forEach(project => {
        const projectName = project.projectName;
        project.checkItems.forEach(item => {
          const { projectNumber, itemName, msrunt, standardValueMin, standardValueMax, result, conclusion } = item;
          console.log('🍊 HealthCheckRegister reportReview item', item);
          bhkSubList.push({
            itmcod: projectNumber || '',
            name: itemName || '',
            classify: projectName || '',
            chkdoct: departmentName,
            msrunt: msrunt || '', // 计量单位
            itemStdValue: `${standardValueMin || ''} - ${standardValueMax || ''}`,
            result: result || '',
            chkdat: doc.registerTime,
            minVal: standardValueMin || '',
            maxVal: standardValueMax || '',
            diagRest: conclusion || '',
            rstFlag: this._getRstFlag(item),
            // jdgptn: conclusion ? 2 : 1,
            rgltag: this._getRgltag(item),
          });
        });
      });
      departmentList.push({
        name: departmentName,
        summary
      });
    });
    const riskFactorsOfPhysicalExaminations = doc.checkHazardFactors.map(item => { return { harmFactor: item.name } });

    const CwithO = doc.jobConclusion.map(ele => {
      let res = '';
      // 1 目前未见异常
      // 2 复查
      // 3 疑似职业病
      // 4 禁忌证
      // 5 其他疾病或异常
      switch (ele) {
        case 1:
          res = '目前未见异常';
          break;
        case 2:
          res = '复查';
          break;
        case 3:
          res = '疑似职业病';
          break;
        case 4:
          res = '职业禁忌证';
          break;
        case 5:
          res = '其他疾病或异常';
          break;
      }
      return res;
    }).join('；');

    // 生成suspect
    const newData = {
      reportCode: doc.checkNo || '',
      organization: PhysicalExamOrg.name, // 检查机构名称
      name: doc.name,
      age,
      gender,
      workType: doc.workType,
      workYears,
      harmFactors,
      // otherHarmFactors: item.hazardFactorList.map(ele => ele.otherHazardName).filter(ele => ele).join('；'), // 其他危害因素
      opinion: doc.suggestion || '', // 意见建议
      CwithO,
      // CwithO: item.diagnosisList.map(ele => [ '目前未见异常', '复查', '疑似职业病', '职业禁忌证', '其他疾病或异常' ][ele.conclusion - 1]).filter(ele => ele).join('；'), // 结论
      dedicalAdvice: doc.jobSummary || '无', // 医学建议
      // batch: healthcheck._id, // 这里没有
      employeeId: doc.employeeId,
      IDCard: doc.idNumber,
      EnterpriseID: doc.EnterpriseID,
      checkType,
      checkDate: doc.registerTime,
      recheck: doc.isRecheck ? '是' : '否',
      // abnormalIndex: abnormalIndex || '-', // 异常指标
      riskFactorsOfPhysicalExaminations, // 体检危险因素
      bhkSubList,
      departmentList,
      healthCheckRegisterId: doc._id,
      source: 'tj',
      manageYear: moment().format('YYYYMMDD'),
    };
    // console.log('🍊 HealthCheckRegister reportReview newData', newData);
    const suspect = await ctx.model.Suspect.create(newData);
    this.ctx.auditLog('体检系统对接 上传体检报告-创建suspect成功', suspect, 'info');

    return res;
  }

  // 获取体检报告详情
  async getHCReportDetail(_id) {
    const { ctx } = this;
    const register = await ctx.model.HealthCheckRegister.findById(_id)
      .populate('checkDepartments.checkProjects.checkItems.itemId')

    if (!register) {
      throw new Error('体检报告不存在');
    }
    return register;
  }

  async findHarmFactors({ current = 1, pageSize = 6, query, searchkey = '', order = 1 } = {}) {
    query = query ? JSON.parse(query) : {};
    for (const key in query) {
      if (!query[key]) delete query[key];
    }
    let searchName = '';
    let isOtherDust = true;
    const pinyinSearchkey = pinyin(searchkey, {
      style: pinyin.STYLE_NORMAL, // 设置拼音风格
    }).join('').toLowerCase();
    if (query.name) {
      searchName = query.name;
      delete query.name;
      pinyinSearchkey ? query.$or = [
        { chineseName: { $regex: searchName } },
        { pinyin: { $regex: pinyinSearchkey } },
      ] : query = { chineseName: { $regex: searchName } };
      if (searchName.indexOf('其他粉尘') === -1) { // 查询得不是其他粉尘
        isOtherDust = false;
        return {
          data: await this.ctx.model.OccupationalexposureLimits.aggregate([
            { $match: query },
            {
              $addFields: {
                diffShowName: { $split: ['$showName', searchName] },
              },
            },
            {
              $addFields: {
                diffShowName: {
                  $map: {
                    input: '$diffShowName',
                    as: 'item',
                    in: { $strLenCP: '$$item' },
                  },
                },
              },
            },
            {
              $addFields: {
                diffShowName: {
                  $reduce: {
                    input: '$diffShowName',
                    initialValue: 1,
                    in: { $add: ['$$value', '$$this'] },
                  },
                },
              },
            },
            { $sort: { diffShowName: 1 } },
            {
              $project: {
                harmFactorName: '$showName', catetory: 1, code: 1, otherName: {
                  $cond: [{ $eq: [searchName, '$showName'] }, [], {
                    $filter: {
                      input: '$chineseName',
                      as: 'item',
                      cond: {
                        $regexMatch: {
                          input: '$$item',
                          regex: searchName,
                        },
                      },
                    },
                  }],

                },
              },
            },
            { $addFields: { otherName: { $arrayElemAt: ['$otherName', 0] } } },
            { $limit: 10 },
          ]),
        };
      }
    }
    if (searchkey) {
      query.$or = [
        { chineseName: { $regex: searchkey } },
        { showName: { $regex: searchkey } },
        { englishName: { $regex: searchkey } },
        { casNum: { $regex: searchkey } },
        { standard: { $regex: searchkey } },
        { pinyin: { $regex: pinyinSearchkey } },
        { catetory: { $regex: pinyinSearchkey } },
      ];
    }

    let dataPipeline = [{ $match: query }];
    const sort = {};
    if (searchkey || (searchName && isOtherDust)) {
      const pipeline = [// 匹配度优先级排序
        { $unwind: '$chineseName' },
        {
          $addFields: {
            diffChineseName: { $split: ['$chineseName', searchkey || searchName] },
          },
        },
        {
          $addFields: {
            diffChineseName: {
              $map: {
                input: '$diffChineseName',
                as: 'item',
                in: { $strLenCP: '$$item' },
              },
            },
          },
        },
        {
          $addFields: {
            diffChineseName: {
              $reduce: {
                input: '$diffChineseName',
                initialValue: 1,
                in: { $add: ['$$value', '$$this'] },
              },
            },
          },
        }];
      if (isOtherDust) {
        pipeline.splice(1, 0, { $match: { chineseName: { $regex: searchName } } });
      }
      dataPipeline = dataPipeline.concat(pipeline);
    }

    if (searchkey) {
      dataPipeline = dataPipeline.concat([
        { $group: { _id: '$_id', info: { $first: '$$ROOT' }, diffChineseName: { $push: '$diffChineseName' }, chineseName: { $push: '$chineseName' } } },
        { $addFields: { 'info.chineseName': '$chineseName', 'info.chineseNameDiffLength': '$chineseNameDiffLength' } },
        { $replaceRoot: { newRoot: '$info' } },
        { $addFields: { diffChineseName: { $min: '$diffChineseName' } } },
      ]);
    }
    sort.diffChineseName = 1;
    sort.initial = Number(order);
    dataPipeline = dataPipeline.concat([
      { $sort: sort },
      { $skip: (current - 1) * pageSize },
      { $limit: parseInt(pageSize) },
      {
        $addFields: {
          respirableDust_standardInfo: {
            $ifNull: ['$respirableDust_standardInfo', { name: '', num: '' }],
          },
          standardInfo: {
            $ifNull: ['$standardInfo', { name: '', num: '' }],
          },
        },
      },
      { $project: { harmFactorName: '$showName', code: 1 } },
    ]);
    if (searchName && isOtherDust) {
      dataPipeline.push({
        $project: {
          harmFactorName: '$chineseName',
          code: 1,
          catetory: 1,
        },
      });
    }
    let res = await this.ctx.model.OccupationalexposureLimits.aggregate([{
      $facet: {
        data: dataPipeline,
        total: [
          { $match: query },
          { $count: 'total' },
        ],
        categorys: [
          { $group: { _id: '$catetory' } },
          {
            $addFields: {
              tableFields: [],
              typeSort: {
                // $indexOfArray 指定某个字段根据特定值排序
                $indexOfArray: [
                  ['粉尘', '化学', '物理', '其他', '放射性因素', '生物'], '$_id',
                ],
              },
            },
          },
          { $sort: { typeSort: 1 } },
        ],
      },
    }]);
    res = JSON.parse(JSON.stringify(res));
    if (res[0]) {
      res = res[0];
      res.total = res.total[0] ? res.total[0].total : 0;
    }
    res.order = Number(order);
    return res;
  }

  // #region 统计
  // 按照年度和examType统计
  async getStatisticsByYear(payload) {
    const { ctx } = this;
    let { startYear, endYear, physicalOrgID } = payload;
    // 默认最近5年，包括当年
    if (!startYear) {
      startYear = new Date().getFullYear() - 4;
    }
    if (!endYear) {
      endYear = new Date().getFullYear();
    }

    const res = await ctx.model.HealthCheckRegister.aggregate([
      {
        $match: {
          registerTime: { // TODO 登记日期，先用这个代替报告日期
            $gte: new Date(`${startYear}-1-1`),
            $lte: new Date(`${endYear}-12-31`),
          },
          status: 3 // 审核通过
        },
      },
      {
        $lookup: {
          from: 'physicalExamOrgs', // 关联的集合名称（注意大小写）
          localField: 'physicalOrgID',
          foreignField: '_id',
          as: 'physicalOrg'
        }
      },
      { $unwind: '$physicalOrg' }, // 展开关联数组
      {
        $match: {
          // 'physicalOrg.regAddr': { $all: superUserInfo.regAdd }
          physicalOrgID
        }
      },
      { $addFields: { year: { $year: '$registerTime' }, }, },
      {
        $group: {
          _id: '$year',
          preJob: { $sum: { $cond: [{ $eq: ['$examType', '1'] }, 1, 0] } },
          onJob: { $sum: { $cond: [{ $eq: ['$examType', '2'] }, 1, 0] } },
          offJob: { $sum: { $cond: [{ $eq: ['$examType', '3'] }, 1, 0] } },
        },
      },
      { $sort: { _id: 1 } },
    ]);

    // 确保所有年份都有数据
    const completeResults = [];
    for (let year = parseInt(startYear); year <= parseInt(endYear); year++) {
      // 查找当前年份的数据
      const yearData = res.find(item => item._id === year);

      if (yearData) {
        // 如果有数据，直接添加
        completeResults.push(yearData);
      } else {
        // 如果没有数据，创建空记录
        completeResults.push({
          _id: year,
          preJob: 0,
          onJob: 0,
          offJob: 0
        });
      }
    }
    return completeResults;
  }
  // 按照年度统计每种危害因素
  // 年度、危害因素、正常异常、examType体检类型
  async getStatisticsByYearAndHarmFactor(payload) {
    const { ctx } = this;
    // 默认最近5年，包括当年
    let { startYear = new Date().getFullYear() - 4, endYear = new Date().getFullYear(), harmfactor, physicalOrgID } = payload;

    // 构建基本查询条件
    const basePipeline = [
      // 添加年份字段
      { $addFields: { year: { $year: '$registerTime' } } },
      // 匹配年份范围
      {
        $match: {
          year: { $gte: parseInt(startYear), $lte: parseInt(endYear) },
          // 确保状态为已完成的体检记录
          status: 3 // 审核通过
        }
      },
      {
        $lookup: {
          from: 'physicalExamOrgs', // 关联的集合名称（注意大小写）
          localField: 'physicalOrgID',
          foreignField: '_id',
          as: 'physicalOrg'
        }
      },
      { $unwind: '$physicalOrg' }, // 展开关联数组
      {
        $match: {
          // 'physicalOrg.regAddr': { $all: superUserInfo.regAdd }
          physicalOrgID
        }
      },
      // 展开危害因素数组
      { $unwind: '$checkHazardFactors' }
    ];

    // 如果指定了危害因素，则添加危害因素的过滤条件
    if (harmfactor) {
      basePipeline.push({
        $match: {
          'checkHazardFactors.name': harmfactor
        }
      });
    }

    // 添加分组和统计条件
    basePipeline.push(
      // 按年份和危害因素分组统计
      {
        $group: {
          _id: {
            year: '$year',
            factor: '$checkHazardFactors.name',
          },
          // 上岗体检统计
          preJob: { $sum: { $cond: [{ $eq: ['$examType', '1'] }, 1, 0] } },
          // 上岗体检正常人次（结论为未见异常）
          preJobNormal: {
            $sum: {
              $cond: [{
                $and: [
                  { $eq: ['$examType', '1'] },
                  {
                    $cond: [
                      { $isArray: ['$checkHazardFactors.conclusion'] },
                      { $eq: [{ $size: { $filter: { input: '$checkHazardFactors.conclusion', as: 'c', cond: { $ne: ['$$c', 1] } } } }, 0] },
                      { $eq: ['$checkHazardFactors.conclusion', 1] }
                    ]
                  }
                ]
              }, 1, 0]
            }
          },
          // 上岗体检异常人次（结论不为未见异常）
          preJobAbnormal: {
            $sum: {
              $cond: [{
                $and: [
                  { $eq: ['$examType', '1'] },
                  {
                    $cond: [
                      { $isArray: ['$checkHazardFactors.conclusion'] },
                      { $gt: [{ $size: { $filter: { input: '$checkHazardFactors.conclusion', as: 'c', cond: { $ne: ['$$c', 1] } } } }, 0] },
                      { $ne: ['$checkHazardFactors.conclusion', 1] }
                    ]
                  }
                ]
              }, 1, 0]
            }
          },
          // 在岗体检统计
          onJob: { $sum: { $cond: [{ $eq: ['$examType', '2'] }, 1, 0] } },
          onJobNormal: {
            $sum: {
              $cond: [{
                $and: [
                  { $eq: ['$examType', '2'] },
                  {
                    $cond: [
                      { $isArray: ['$checkHazardFactors.conclusion'] },
                      { $eq: [{ $size: { $filter: { input: '$checkHazardFactors.conclusion', as: 'c', cond: { $ne: ['$$c', 1] } } } }, 0] },
                      { $eq: ['$checkHazardFactors.conclusion', 1] }
                    ]
                  }
                ]
              }, 1, 0]
            }
          },
          onJobAbnormal: {
            $sum: {
              $cond: [{
                $and: [
                  { $eq: ['$examType', '2'] },
                  {
                    $cond: [
                      { $isArray: ['$checkHazardFactors.conclusion'] },
                      { $gt: [{ $size: { $filter: { input: '$checkHazardFactors.conclusion', as: 'c', cond: { $ne: ['$$c', 1] } } } }, 0] },
                      { $ne: ['$checkHazardFactors.conclusion', 1] }
                    ]
                  }
                ]
              }, 1, 0]
            }
          },
          // 离岗体检统计
          offJob: { $sum: { $cond: [{ $eq: ['$examType', '3'] }, 1, 0] } },
          offJobNormal: {
            $sum: {
              $cond: [{
                $and: [
                  { $eq: ['$examType', '3'] },
                  {
                    $cond: [
                      { $isArray: ['$checkHazardFactors.conclusion'] },
                      { $eq: [{ $size: { $filter: { input: '$checkHazardFactors.conclusion', as: 'c', cond: { $ne: ['$$c', 1] } } } }, 0] },
                      { $eq: ['$checkHazardFactors.conclusion', 1] }
                    ]
                  }
                ]
              }, 1, 0]
            }
          },
          offJobAbnormal: {
            $sum: {
              $cond: [{
                $and: [
                  { $eq: ['$examType', '3'] },
                  {
                    $cond: [
                      { $isArray: ['$checkHazardFactors.conclusion'] },
                      { $gt: [{ $size: { $filter: { input: '$checkHazardFactors.conclusion', as: 'c', cond: { $ne: ['$$c', 1] } } } }, 0] },
                      { $ne: ['$checkHazardFactors.conclusion', 1] }
                    ]
                  }
                ]
              }, 1, 0]
            }
          },
        },
      }
    );

    // 根据不同的查询需求添加排序条件
    if (harmfactor) {
      // 如果指定了危害因素，按年份排序（用于第四个tab）
      basePipeline.push({ $sort: { "_id.year": 1 } });
    } else {
      // 如果没有指定危害因素，按危害因素名称排序（用于第二个tab）
      basePipeline.push({ $sort: { "_id.factor": 1 } });
    }

    // 执行聚合查询
    const res = await ctx.model.HealthCheckRegister.aggregate(basePipeline);

    // 如果指定了危害因素，需要确保所有年份都有数据（用于第四个tab）
    if (harmfactor) {
      const completeResults = [];
      for (let year = parseInt(startYear); year <= parseInt(endYear); year++) {
        // 查找当前年份的数据
        const yearData = res.find(item => item._id.year === year);

        if (yearData) {
          // 如果有数据，直接添加
          completeResults.push(yearData);
        } else {
          // 如果没有数据，创建空记录
          completeResults.push({
            _id: {
              year: year,
              factor: harmfactor
            },
            preJob: 0,
            preJobNormal: 0,
            preJobAbnormal: 0,
            onJob: 0,
            onJobNormal: 0,
            onJobAbnormal: 0,
            offJob: 0,
            offJobNormal: 0,
            offJobAbnormal: 0
          });
        }
      }
      return completeResults; // 返回结果集，包含指定危害因素在每一年的统计数据
    } else {
      // 如果没有指定危害因素，直接返回查询结果（用于第二个tab）
      return res;
    }
  }

  async findHarmFactors2({ current = 1, pageSize = 6, query, searchkey = '', order = 1 } = {}) {
    query = query ? JSON.parse(query) : {};
    for (const key in query) {
      if (!query[key]) delete query[key];
    }

    // 处理模糊查询
    let searchName = '';
    if (query.name) {
      searchName = query.name;
      delete query.name;

      // 构建模糊查询条件
      query.$or = [
        { showName: { $regex: searchName, $options: 'i' } },  // 不区分大小写
        { chineseName: { $regex: searchName, $options: 'i' } },
        { englishName: { $regex: searchName, $options: 'i' } },
        { casNum: { $regex: searchName, $options: 'i' } }
      ];
    }

    // 如果有关键字搜索
    if (searchkey) {
      query.$or = [
        { chineseName: { $regex: searchkey, $options: 'i' } },
        { showName: { $regex: searchkey, $options: 'i' } },
        { englishName: { $regex: searchkey, $options: 'i' } },
        { casNum: { $regex: searchkey, $options: 'i' } },
        { standard: { $regex: searchkey, $options: 'i' } },
      ];
    }

    // 构建查询管道
    const dataPipeline = [
      { $match: query },
      { $sort: { showName: 1 } }, // 按名称排序
      { $skip: (current - 1) * pageSize },
      { $limit: parseInt(pageSize) },
      // 投影出需要的字段
      {
        $project: {
          _id: 1,
          harmFactorName: '$showName',
          catetory: 1,
          code: 1
        }
      }
    ];

    // 执行聚合查询
    let res = await this.ctx.model.OccupationalexposureLimits.aggregate([{
      $facet: {
        data: dataPipeline,
        total: [
          { $match: query },
          { $count: 'total' },
        ],
        categorys: [
          { $group: { _id: '$catetory' } },
          {
            $addFields: {
              tableFields: [],
              typeSort: {
                // $indexOfArray 指定某个字段根据特定值排序
                $indexOfArray: [
                  ['粉尘', '化学', '物理', '其他', '放射性因素', '生物'], '$_id',
                ],
              },
            },
          },
          { $sort: { typeSort: 1 } },
        ],
      },
    }]);

    // 处理结果
    res = JSON.parse(JSON.stringify(res));
    if (res[0]) {
      res = res[0];
      res.total = res.total[0] ? res.total[0].total : 0;
    }
    res.order = Number(order);
    return res;
  }
  // #endregion


  // #region 内部方法
  _getRstFlag(item) {
    // 结果判定标记 '异常', '未见异常', '尘肺样改变', '其他异常'
    if (item.standardValueMin && item.standardValueMax
      && item.result > item.standardValueMin && item.result < item.standardValueMax) {
      return '未见异常';
    } else {
      return '异常'
    }
  }

  _getJdgptn(item) {
    // 判断模式 1：定性2：定量
  }

  _getRgltag(item) {
    // 合格标记 1：合格0：不合格
    if (item.standardValueMin && item.standardValueMax
      && item.result > item.standardValueMin && item.result < item.standardValueMax) {
      return 1;
    } else {
      return 0
    }
  }

  // 私有方法：生成体检编号
  async _generateCheckNo(employeeId) {
    const dataString = moment().format('YYYYMMDD');
    const checkNo = `${dataString}${employeeId.slice(0, 4)}${nanoid(4)}`
    return checkNo;
  }

  // 私有方法：验证登记数据
  _validateRegisterData(data) {
    const requiredFields = ['name', 'gender', 'idNumber', 'birthDate', 'checkType', 'examType'];
    for (const field of requiredFields) {
      if (!data[field]) {
        throw new Error(`${field}不能为空`);
      }
    }

    // 验证身份证号格式
    if (data.idType === '1' && !/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/.test(data.idNumber)) {
      throw new Error('身份证号格式不正确');
    }

    // 验证手机号格式
    if (data.phone && !/^1[3-9]\d{9}$/.test(data.phone)) {
      throw new Error('手机号格式不正确');
    }

    // 验证工龄
    if (data.totalWorkYears < 0 || data.totalWorkMonths < 0 || data.totalWorkMonths > 11) {
      throw new Error('工龄填写不正确');
    }
    if (data.exposureWorkYears < 0 || data.exposureWorkMonths < 0 || data.exposureWorkMonths > 11) {
      throw new Error('接触工龄填写不正确');
    }
  }

  // 私有方法：根据ID查找登记记录
  async _findRegisterById(_id) {
    const { ctx } = this;
    const register = await ctx.model.HealthCheckRegister.findById(_id);
    if (!register) {
      throw new Error('体检登记记录不存在');
    }
    return register;
  }

  // 更新体检登记表的 操作人 和 操作时间
  async _updateOperator(checkNo) {
    const { ctx } = this;
    const userInfo = ctx.session.physicalExamUserInfo
    const user = await ctx.model.PhysicalExamUser.findOne({ _id: userInfo._id });
    const reginster = ctx.model.HealthCheckRegister.findOne({ checkNo })
    if (!reginster) {
      throw new Error('体检记录不存在');
    }
    const res = await ctx.model.HealthCheckRegister.updateOne(
      { checkNo },
      {
        $set: {
          operator: user.name,
          operateTime: new Date()
        }
      }
    )
    return res;
  }
}

module.exports = HealthCheckRegisterService;