<template>
  <div class="app-container importConstruction">
    <div style="display: flex">
      <page-header title="健康检查项目批量导入"> </page-header>
      <el-link icon="el-icon-question" type="primary" @click="info">引导</el-link>
    </div>

    <upload-excel-component :submit="submit" :on-success="handleSuccess" :before-upload="beforeUpload"
      @downloadTemplate="downloadTemplate" @deleteMany="deleteMany" :multipleSelection="multipleSelection">
      <el-button type="primary" size="small" @click="addNewRow">添加</el-button>
    </upload-excel-component>
    <el-form class="excelForm">
      <div style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
        <div>
          <el-button type="danger" size="small" @click="deleteMany" :disabled="multipleSelection.length === 0">
            批量删除 ({{ multipleSelection.length }})
          </el-button>
        </div>
        <div>
          <span style="color: #909399; font-size: 12px;">
            已选择 {{ multipleSelection.length }} 项，共 {{ tableData.length }} 项
          </span>
        </div>
      </div>
      <pl-table @selection-change="handleSelectionChange" :data="tableData" border height="600px" use-virtual
        highlight-current-row style="width: 100%; margin-top: 20px">
        <pl-table-column type="selection" width="55"> </pl-table-column>
        <pl-table-column v-for="item of tableHeader" :key="item" :prop="item" :label="item"
          :width="getColumnWidth(item)">
          <template slot-scope="scope">
            <el-form-item>
              <component :is="getInputComponent(item)" v-model="tableData[scope.$index][item]"
                v-bind="getInputProps(item)" @change="validateRow(scope.$index)">
                <template v-if="getInputComponent(item) === 'el-select'">
                  <el-option v-for="option in getSelectOptions(item)" :key="option.value" :label="option.label"
                    :value="option.value" />
                </template>
              </component>
            </el-form-item>
          </template>
        </pl-table-column>
        <pl-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="danger" size="mini" @click="deleteItem(scope.$index)">删除</el-button>
          </template>
        </pl-table-column>
      </pl-table>
    </el-form>
    <import-tips :requireError="requireError" :dialogVisible.sync="dialogTip" :touchTimeErr="touchTimeErr">
    </import-tips>
    <el-dialog :close-on-click-modal="false" :visible.sync="showSaveLoad" :show-close="false" width="0">
      <ProgressLoad :percentage="percentage" :percentageItem="percentageItem" :tableDataLength="ProgressLength">
      </ProgressLoad>
    </el-dialog>
  </div>
</template>

<script>
import ProgressLoad from "@/components/ProgressLoad/ProgressLoad.vue";
import Driver from "driver.js";
import { PlTable, PlTableColumn } from "pl-table";
import "driver.js/dist/driver.min.css";
import ImportTips from "@/components/importTips/index";
import pageHeader from "@/components/pageHeader.vue";
import UploadExcelComponent from "@/components/UploadExcel/index.vue";
import { batchImportHealthItems, getResultTypeOption, getHealthCheckUnit } from '@/api/index'
import { downloadHealthCheckTemplate, validateExcelData } from '@/utils/excelTemplate'
const driver = new Driver({
  allowClose: false,
  doneBtnText: "结束引导",
  closeBtnText: "关闭引导",
  nextBtnText: "下一步",
  prevBtnText: "上一步",
});
export default {
  name: "UploadExcel",
  components: {
    PlTable,
    PlTableColumn,
    UploadExcelComponent,
    pageHeader,
    ImportTips,
    ProgressLoad,
  },
  data() {
    return {
      showSaveLoad: false,
      driver: null,
      dialogTip: false,
      requireError: [], //存在必填项未填
      touchTimeErr: "", //接触时间格式错误
      dialogVisible: false,
      tableData: [],
      tableHeader: [],
      importData: [],
      multipleSelection: [],
      socket: null,
      socketID: null,
      percentage: 0, // 进度条进度
      percentageItem: 0, // 当前第几个
      ProgressRatio: 0, // 每个的进度
      ProgressLength: 0, // 进度总的个数
      resultTypeOptions: [], // 结果类型选项
      unitOption: [], // 单位选项
      validationErrors: {}, // 验证错误信息
    };
  },
  created() {
    this.getOptions();
    setTimeout(() => {
      this.info();
    }, 50);
  },
  beforeDestroy() {
    if (this.socket) {
      this.socket.disconnect();
    }
  },
  methods: {
    // 获取选项数据
    async getOptions() {
      try {
        const [resultTypeRes, unitRes] = await Promise.all([
          getResultTypeOption(),
          getHealthCheckUnit()
        ]);

        if (resultTypeRes.status === 200) {
          this.resultTypeOptions = resultTypeRes.data || [];
        }
        if (unitRes.status === 200) {
          this.unitOption = unitRes.data || [];
        }
      } catch (error) {
        console.error('获取选项数据失败:', error);
      }
    },

    // 获取列宽度
    getColumnWidth(columnName) {
      const widthMap = {
        '项目名称': 150,
        '项目编号': 120,
        '结果类型': 100,
        '计量单位': 100,
        '性别限制': 100,
        '婚姻限制': 100,
        '适用类型': 240,
        '是否限制年龄': 120,
        '最小年龄': 100,
        '最大年龄': 100,
        '是否有参考范围': 130,
        '参考值下限': 120,
        '参考值上限': 120,
        '是否有极值范围': 130,
        '最小极值': 120,
        '最大极值': 120,
        // '偏高提示': 150,
        // '偏低提示': 150
      };
      return widthMap[columnName] || 120;
    },

    // 获取输入组件类型
    getInputComponent(columnName) {
      const selectFields = ['结果类型', '计量单位', '性别限制', '婚姻限制', '适用类型', '是否限制年龄', '是否有参考范围', '是否有极值范围'];
      return selectFields.includes(columnName) ? 'el-select' : 'el-input';
    },

    // 获取输入组件属性
    getInputProps(columnName) {
      const props = { size: 'small', placeholder: `请输入${columnName}` };

      if (columnName === '结果类型') {
        return {
          ...props,
          placeholder: '请选择结果类型'
        };
      } else if (columnName === '计量单位') {
        return {
          ...props,
          placeholder: '请选择计量单位',
          clearable: true
        };
      } else if (columnName === '适用类型') {
        return {
          ...props,
          placeholder: '请选择适用类型',
          multiple: true,
          'collapse-tags': true
        };
      } else if (['性别限制', '婚姻限制'].includes(columnName)) {
        return {
          ...props,
          placeholder: `请选择${columnName}`
        };
      } else if (['是否限制年龄', '是否有参考范围', '是否有极值范围'].includes(columnName)) {
        return {
          ...props,
          placeholder: '请选择是否'
        };
      }

      return props;
    },

    // 获取选择组件的选项
    getSelectOptions(columnName) {
      if (columnName === '结果类型') {
        return this.resultTypeOptions.map(opt => ({ label: opt.name, value: opt.name }));
      } else if (columnName === '计量单位') {
        return this.unitOption.map(opt => ({ label: opt.name, value: opt.name }));
      } else if (columnName === '适用类型') {
        return [
          { label: '职业健康检查项目', value: '职业健康检查项目' },
          { label: '平时体检项目', value: '平时体检项目' }
        ];
      } else if (columnName === '性别限制') {
        return [
          { label: '不限', value: '不限' },
          { label: '男', value: '男' },
          { label: '女', value: '女' }
        ];
      } else if (columnName === '婚姻限制') {
        return [
          { label: '不限', value: '不限' },
          { label: '未婚', value: '未婚' },
          { label: '已婚', value: '已婚' }
        ];
      } else if (['是否限制年龄', '是否有参考范围', '是否有极值范围'].includes(columnName)) {
        return [
          { label: '是', value: '是' },
          { label: '否', value: '否' }
        ];
      }

      return [];
    },

    // 验证单行数据
    validateRow(index) {
      const row = this.tableData[index];
      const errors = [];

      // 必填项验证
      if (!row['项目名称']) {
        errors.push('项目名称不能为空');
      }
      if (!row['项目编号']) {
        errors.push('项目编号不能为空');
      }
      if (!row['结果类型']) {
        errors.push('结果类型不能为空');
      }
      if (!row['适用类型'] || (Array.isArray(row['适用类型']) && row['适用类型'].length === 0)) {
        errors.push('适用类型不能为空');
      }

      // 设置默认值
      this.setDefaultValues(row);

      // 年龄限制验证
      if (row['是否限制年龄'] === '是') {
        if (!row['最小年龄'] || !row['最大年龄']) {
          errors.push('启用年龄限制时，最小年龄和最大年龄不能为空');
        } else if (Number(row['最小年龄']) >= Number(row['最大年龄'])) {
          errors.push('最小年龄必须小于最大年龄');
        }
      }

      // 参考范围验证
      if (row['是否有参考范围'] === '是') {
        if (!row['参考值下限'] || !row['参考值上限']) {
          errors.push('启用参考范围时，参考值下限和上限不能为空');
        } else if (Number(row['参考值下限']) >= Number(row['参考值上限'])) {
          errors.push('参考值下限必须小于上限');
        }
      }

      // 极值范围验证
      if (row['是否有极值范围'] === '是') {
        if (!row['最小极值'] || !row['最大极值']) {
          errors.push('启用极值范围时，最小极值和最大极值不能为空');
        } else if (Number(row['最小极值']) >= Number(row['最大极值'])) {
          errors.push('最小极值必须小于最大极值');
        }
      }

      // 更新验证错误
      if (errors.length > 0) {
        this.$set(this.validationErrors, index, errors);
      } else {
        this.$delete(this.validationErrors, index);
      }
    },

    // 设置默认值
    setDefaultValues(row) {
      // 性别限制默认为不限
      if (!row['性别限制']) {
        row['性别限制'] = '不限';
      }
      // 婚姻限制默认为不限
      if (!row['婚姻限制']) {
        row['婚姻限制'] = '不限';
      }
      // 是否限制年龄默认为否
      if (!row['是否限制年龄']) {
        row['是否限制年龄'] = '否';
      }
      // 是否有参考范围默认为否
      if (!row['是否有参考范围']) {
        row['是否有参考范围'] = '否';
      }
      // 是否有极值范围默认为否
      if (!row['是否有极值范围']) {
        row['是否有极值范围'] = '否';
      }
    },
    async info() {
      driver.defineSteps([
        {
          element: ".downloadBtn",
          popover: {
            className: "first-step-popover-class",
            title: "下载模板（1/4）",
            description: "提供健康检查项目Excel模板下载，您可以参照Excel模板来完善您的Excel",
            position: "bottom",
          },
        },
        {
          element: ".importExcel",
          popover: {
            className: "first-step-popover-class",
            title: "导入Excel（2/4）",
            description: "点击导入，选择对应的Excel文件，即可导入成功",
            position: "bottom",
          },
        },
        {
          element: ".excelForm",
          popover: {
            className: "first-step-popover-class",
            title: "检查信息（3/4）",
            description: "查看导入的健康检查项目信息，可点击对应的信息进行编辑修改",
            position: "top",
          },
        },
        {
          element: ".submitBtn",
          popover: {
            className: "first-step-popover-class",
            title: "保存信息（4/4）",
            description: "信息检查完毕后，保存即可提交健康检查项目信息",
            position: "left",
          },
        },
      ]);
      driver.start();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    deleteMany() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请先选择要删除的数据');
        return;
      }

      this.$confirm(`确定删除所选的 ${this.multipleSelection.length} 条数据？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        // 从后往前删除，避免索引变化问题
        const indicesToDelete = this.multipleSelection.map(item =>
          this.tableData.indexOf(item)
        ).sort((a, b) => b - a);

        indicesToDelete.forEach(index => {
          if (index !== -1) {
            this.tableData.splice(index, 1);
          }
        });

        // 清空选择状态
        this.multipleSelection = [];

        this.$message.success(`成功删除 ${indicesToDelete.length} 条数据`);
      });
    },
    deleteItem(i) {
      this.tableData.splice(i, 1);
    },
    // 添加新行
    addNewRow() {
      if (this.tableHeader.length === 0) {
        // 如果还没有表头，设置默认表头
        this.tableHeader = [
          '项目名称', '项目编号', '结果类型', '计量单位', '性别限制', '婚姻限制',
          '适用类型', '是否限制年龄', '最小年龄', '最大年龄', '是否有参考范围',
          '参考值下限', '参考值上限', '是否有极值范围', '最小极值', '最大极值'
        ];
      }

      // 创建新行数据，设置默认值
      const newRow = {};
      this.tableHeader.forEach(header => {
        if (header === '性别限制') {
          newRow[header] = '不限';
        } else if (header === '婚姻限制') {
          newRow[header] = '不限';
        } else if (header === '适用类型') {
          newRow[header] = [];
        } else if (['是否限制年龄', '是否有参考范围', '是否有极值范围'].includes(header)) {
          newRow[header] = '否';
        } else {
          newRow[header] = '';
        }
      });

      this.tableData.push(newRow);
    },
    downloadTemplate() {
      downloadHealthCheckTemplate();
      driver.isActivated && driver.moveNext();
    },
    beforeUpload(file) {
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (isLt1M) {
        return true;
      }
      this.$message({
        message: "Please do not upload files larger than 1m in size.",
        type: "warning",
      });
      return false;
    },
    async submit() {
      let requireError = [];
      driver.isActivated && driver.reset();

      if (this.tableData.length === 0) {
        return this.$message.warning("请先上传excel文件");
      }

      // 验证所有行数据
      this.tableData.forEach((_, index) => {
        this.validateRow(index);

        // 收集验证错误
        if (this.validationErrors[index]) {
          this.validationErrors[index].forEach(error => {
            requireError.push(`第${index + 1}行：${error}`);
          });
        }
      });

      // 去重错误信息
      requireError = Array.from(new Set(requireError));
      this.requireError = requireError;

      if (requireError.length > 0) {
        this.dialogTip = true;
        return;
      }

      // 转换数据格式
      this.importData = this.tableData.map(item => this.convertRowToApiFormat(item));

      try {
        // 调用批量导入API
        const res = await batchImportHealthItems({ items: this.importData });
        if (res.status === 200) {
          const result = res.data;
          // 根据后端返回的详细信息展示更精确的提示
          if (result.failCount > 0) {
            this.$message.warning(`导入部分成功：成功${result.successCount}条，失败${result.failCount}条`);
          } else {
            this.$message.success(result.message || `批量导入成功，共导入${result.successCount}条数据`);
          }
          setTimeout(() => {
            this.$router.push({ name: 'index' });
          }, 1000);
        } else {
          this.$message.error('批量导入失败：' + (res.message || '未知错误'));
        }
      } catch (error) {
        console.error('批量导入错误:', error);
        // 目前后端接口尚未开发，先收集数据用于接口开发参考
        console.log('批量导入数据结构:', this.importData);
        this.$message.success(`导入失败`);
      }
    },

    // 将Excel行数据转换为API格式
    convertRowToApiFormat(row) {
      return {
        projectName: row['项目名称'] || '',
        projectNumber: row['项目编号'] || '',
        resultType: this.convertResultType(row['结果类型']),
        msrunt: this.convertUnit(row['计量单位']),
        genderLimit: this.convertGenderLimit(row['性别限制']),
        marriage: this.convertMarriage(row['婚姻限制']),
        suitType: this.convertSuitType(row['适用类型']),
        isLimitAge: this.convertYesNo(row['是否限制年龄']),
        ageLimitMin: this.convertNumber(row['最小年龄']),
        ageLimitMax: this.convertNumber(row['最大年龄']),
        isStandard: this.convertYesNo(row['是否有参考范围']),
        standardValueMin: this.convertNumber(row['参考值下限']),
        standardValueMax: this.convertNumber(row['参考值上限']),
        isExtremeValue: this.convertYesNo(row['是否有极值范围']),
        extremeValueMin: this.convertNumber(row['最小极值']),
        extremeValueMax: this.convertNumber(row['最大极值']),
        // highValueTips: row['偏高提示'] || '',
        // lowValueTips: row['偏低提示'] || ''
      };
    },

    // 转换结果类型
    convertResultType(value) {
      if (!value) return '';
      const option = this.resultTypeOptions.find(opt => opt.name === value);
      return option ? option.value : value;
    },

    // 转换单位
    convertUnit(value) {
      if (!value) return '';
      const option = this.unitOption.find(opt => opt.name === value);
      return option ? option.value : value;
    },

    // 转换性别限制
    convertGenderLimit(value) {
      const map = { '不限': 1, '男': 2, '男性': 2, '女': 3, '女性': 3 };
      return map[value] || 1;
    },

    // 转换婚姻限制
    convertMarriage(value) {
      const map = { '不限': 1, '未婚': 2, '已婚': 3 };
      return map[value] || 1;
    },

    // 转换适用类型
    convertSuitType(value) {
      if (!value) return [1];

      // 如果已经是数组，直接处理
      if (Array.isArray(value)) {
        const types = [];
        value.forEach(item => {
          if (item === '职业健康检查项目' || item.includes('职业健康检查项目')) {
            types.push(1);
          }
          if (item === '平时体检项目' || item.includes('平时体检项目') || item.includes('平时检查项目')) {
            types.push(2);
          }
        });
        return types.length > 0 ? types : [1];
      }

      // 如果是字符串，按原逻辑处理
      const types = [];
      if (value.includes('职业健康检查项目')) types.push(1);
      if (value.includes('平时体检项目') || value.includes('平时检查项目')) types.push(2);
      return types.length > 0 ? types : [1];
    },

    // 转换是否类型
    convertYesNo(value) {
      const map = { '是': 1, '否': 2, 'true': 1, 'false': 2, '1': 1, '0': 2 };
      return map[String(value)] || 2;
    },

    // 转换数字
    convertNumber(value) {
      if (!value || value === '') return null;
      const num = Number(value);
      return isNaN(num) ? null : num;
    },
    async handleSuccess({ results, header }) {
      // 验证Excel数据格式
      const validation = validateExcelData(results);
      if (!validation.isValid) {
        this.$message.error('Excel格式错误：' + validation.errors.join('；'));
        return;
      }

      // 处理导入的数据，确保适用类型字段格式正确
      this.tableData = results.map(row => {
        // 如果适用类型是字符串，转换为数组
        if (row['适用类型'] && typeof row['适用类型'] === 'string') {
          const suitTypes = [];
          if (row['适用类型'].includes('职业健康检查项目')) {
            suitTypes.push('职业健康检查项目');
          }
          if (row['适用类型'].includes('平时体检项目') || row['适用类型'].includes('平时检查项目')) {
            suitTypes.push('平时体检项目');
          }
          row['适用类型'] = suitTypes;
        } else {
          row['适用类型'] = [];
        }
        // 添加对 el-input 对应值导入的处理
        if (row['计量单位'] && typeof row['计量单位'] === 'string') {
          row['计量单位'] = row['计量单位'].trim();
        }
        return row;
      });

      this.tableHeader = header;

      // 初始化验证错误对象
      this.validationErrors = {};

      driver.isActivated && driver.moveNext();
    },
  },
};
</script>

<style scoped>
.submitBtn {
  margin: 30px auto;
  display: block;
}
</style>
