import * as XLSX from 'xlsx'

// 创建健康检查项目导入模板
export function createHealthCheckTemplate() {
    // 模板数据
    const templateData = [
        {
            '项目名称': '血常规',
            '项目编号': 'XCG001',
            '结果类型': '定量',
            '计量单位': 'g/L',
            '性别限制': '不限',
            '婚姻限制': '不限',
            '适用类型': '职业健康检查项目',
            '是否限制年龄': '是',
            '最小年龄': '18',
            '最大年龄': '65',
            '是否有参考范围': '是',
            '参考值下限': '120',
            '参考值上限': '160',
            '是否有极值范围': '否',
            '最小极值': '',
            '最大极值': '',
        },
        {
            '项目名称': '尿常规',
            '项目编号': 'NCG002',
            '结果类型': '定性',
            '计量单位': '',
            '性别限制': '不限',
            '婚姻限制': '不限',
            '适用类型': '职业健康检查项目/平时体检项目',
            '是否限制年龄': '否',
            '最小年龄': '',
            '最大年龄': '',
            '是否有参考范围': '否',
            '参考值下限': '',
            '参考值上限': '',
            '是否有极值范围': '否',
            '最小极值': '',
            '最大极值': '',
        },
        {
            '项目名称': '胸部X光',
            '项目编号': 'XBX003',
            '结果类型': '定性',
            '计量单位': '',
            '性别限制': '不限',
            '婚姻限制': '不限',
            '适用类型': '职业健康检查项目/平时体检项目',
            '是否限制年龄': '是',
            '最小年龄': '16',
            '最大年龄': '70',
            '是否有参考范围': '否',
            '参考值下限': '',
            '参考值上限': '',
            '是否有极值范围': '否',
            '最小极值': '',
            '最大极值': '',
        }
    ]

    // 说明信息
    const instructions = [
        ['字段说明', ''],
        ['项目名称', '必填，健康检查项目的名称'],
        ['项目编号', '必填，项目的编号'],
        ['结果类型', '必填，可选值：定性、定量'],
        ['计量单位', '选填，如：g/L、mg/dL等，可为空'],
        ['性别限制', '选填，可选值：不限、男、女，默认：不限'],
        ['婚姻限制', '选填，可选值：不限、未婚、已婚，默认：不限'],
        ['适用类型', '必填，可选值：职业健康检查项目、平时体检项目，多个用/分隔'],
        ['是否限制年龄', '选填，可选值：是、否，默认：否'],
        ['最小年龄', '当"是否限制年龄"为"是"时必填，数字'],
        ['最大年龄', '当"是否限制年龄"为"是"时必填，数字'],
        ['是否有参考范围', '选填，可选值：是、否，默认：否'],
        ['参考值下限', '当"是否有参考范围"为"是"时必填，数字'],
        ['参考值上限', '当"是否有参考范围"为"是"时必填，数字'],
        ['是否有极值范围', '选填，可选值：是、否，默认：否'],
        ['最小极值', '当"是否有极值范围"为"是"时必填，数字'],
        ['最大极值', '当"是否有极值范围"为"是"时必填，数字'],
        ['', ''],
        ['注意事项', ''],
        ['1. 项目名称、项目编号、结果类型、适用类型为必填项', ''],
        ['2. 计量单位可以为空，其他选填项有默认值', ''],
        ['3. 数值类型字段请填写数字', ''],
        ['4. 年龄、参考值、极值的最小值必须小于最大值', ''],
        ['5. 适用类型可以多选，用/分隔', ''],
        ['6. 请严格按照可选值填写选择类型字段', ''],
        ['7. 导入后可使用"添加"按钮手动添加新行', ''],
        ['8. 支持批量删除已勾选的行', '']
    ]

    // 创建工作簿
    const wb = XLSX.utils.book_new()

    // 创建数据工作表
    const dataWs = XLSX.utils.json_to_sheet(templateData)
    XLSX.utils.book_append_sheet(wb, dataWs, '数据模板')

    // 创建说明工作表
    const instructionWs = XLSX.utils.aoa_to_sheet(instructions)
    XLSX.utils.book_append_sheet(wb, instructionWs, '填写说明')

    return wb
}

// 下载模板文件
export function downloadHealthCheckTemplate() {
    const wb = createHealthCheckTemplate()
    XLSX.writeFile(wb, '健康检查项目导入模板.xlsx')
}

// 验证Excel数据格式
export function validateExcelData(data) {
    const requiredFields = ['项目名称', '项目编号', '结果类型', '适用类型']
    const errors = []

    if (!data || !Array.isArray(data) || data.length === 0) {
        errors.push('Excel文件为空或格式不正确')
        return { isValid: false, errors }
    }

    // 检查必需的列是否存在
    const firstRow = data[0]
    const missingFields = requiredFields.filter(field => !(field in firstRow))

    if (missingFields.length > 0) {
        errors.push(`缺少必需的列：${missingFields.join('、')}`)
    }

    // 检查是否有有效数据行
    const validRows = data.filter(row =>
        row['项目名称'] && row['项目编号'] && row['结果类型'] && row['适用类型']
    )
    if (validRows.length === 0) {
        errors.push('没有找到有效的数据行（必须包含项目名称、项目编号、结果类型、适用类型）')
    }

    return {
        isValid: errors.length === 0,
        errors,
        validRowCount: validRows.length
    }
}

// 数据类型转换工具
export const dataConverters = {
    // 转换性别限制
    convertGender(value) {
        const map = { '不限': 1, '男': 2, '男性': 2, '女': 3, '女性': 3 }
        return map[value] || 1
    },

    // 转换婚姻限制
    convertMarriage(value) {
        const map = { '不限': 1, '未婚': 2, '已婚': 3 }
        return map[value] || 1
    },

    // 转换是否类型
    convertBoolean(value) {
        const map = { '是': 1, '否': 2, 'true': 1, 'false': 2, '1': 1, '0': 2 }
        return map[String(value)] || 2
    },

    // 转换适用类型
    convertSuitType(value) {
        if (!value) return [1]
        const types = []
        if (value.includes('职业健康检查项目')) types.push(1)
        if (value.includes('平时体检项目') || value.includes('平时检查项目')) types.push(2)
        return types.length > 0 ? types : [1]
    },

    // 转换数字
    convertNumber(value) {
        if (!value || value === '') return null
        const num = Number(value)
        return isNaN(num) ? null : num
    }
}
