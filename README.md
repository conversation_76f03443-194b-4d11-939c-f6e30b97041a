体检机构端

# 体检端 版本 更新日志
## 1.0.5xx_xjbt更新日志 
- 体检端
  - 体检记录HealthCheckRegister记录操作人和操作时间。当前只记录最后一次的操作，以下操作会触发更新：
    体检登记、结果录入、科室小结、检查总结
  - 检查项目：批量导入前端校验更新，查看时补充极值范围、查看时去掉必填星号，导入和新增校验
## 1.0.564_xjbt更新日志 
- 体检端
  - 检查项目批量导入
## 1.0.563_xjbt更新日志 
- 体检端
  - 预约处理：批量处理，增加登记状态的展示，增加跳转体检登记表
  - 费用管理：体检机构隔离
  - 体检登记：
    总结后不可修改。
    自动抓取部门、工种、接触的危害因素。
    填入身份证号自动获取信息（排序方式  1未登记优先 2最新创建 PS：同意体检预约时创建体检登记表）
## 1.0.561_xjbt更新日志 
- 体检端
  - 修复备案校验
## 1.0.560_xjbt更新日志 
- 体检端
  - 修复补正材料
## 1.0.559_xjbt更新日志 
- 体检端
  - 增加备案登记表的生成,修复补正材料
## 1.0.558_xjbt更新日志 
- 体检端 fix
  - 实现职业健康检查过程管理-查看体检登记 用人单位回显有问题
  - 实现职业健康检查过程管理-新增体检登记 出生日期自动获取计算错误，缺少工号字段
  - 实现职业健康检查过程管理-结果录入 基本信息中检查类型错误
  - 实现职业健康检查过程管理-科室小结 基本信息中检查类型错误
  - 实现职业健康检查过程管理-检查总结 基本信息中检查类型错误
  - 实现职业健康检查统计
  - 实现职业健康检查设置-新增预约时间设置 前后端校验不一致
  - 实现职业健康检查设置-编辑预约时间设置 前后端校验不一致
  - 编辑机构设置
## 1.0.556_xjbt更新日志 
- 体检端
  - 体检备案修复
## 1.0.555_xjbt更新日志 
- 体检端
  - 体检备案数据隔离
## 1.0.554_xjbt更新日志 
- 体检端
  - 体检备案修复
## 1.0.553_xjbt更新日志 
- 体检端
  - 体检预约通过逻辑调整：先查找employeeId和contractId是否有相同的记录，如果有则更新，没有则创建。而不是直接创建 => 解决【用人单位】疑似职业病上报，体检是1条数据，显示了3条数据
  - 体检报告列表机构id过滤
  - 职业健康检查费用管理，实收金额允许编辑，默认为0 ;分页
  - 收费项目保存按钮导致已选项目清空
  - 职检结论编码统一 [12001,12002,12003,12004,12005] => [1,2,3,4,5] (老数据可能会有问题)
  - 科室小结提交后回显、取消小结操作提示fix
  - 体检总结危害因素的结论多选
  - 体检登记选择体检的危害因素时保存code

## 1.0.552_xjbt更新日志 
- 体检端
  - 过程管理打包
  - fix用人单位新增
## 1.0.551_xjbt更新日志 
- 体检端
  - 检查项目编辑时填入值为0被判为未填写
  - 新增用人单位报错
  - 收费项目添加年龄限制
  - 收费项目基础信息保存
  - 职业健康检查费用机构隔离（体检机构id）
  - 同意预约逻辑调整、错误处理
  - 检查总结危害因素结论选择
## 1.0.550_xjbt更新日志 
- 体检端
  - 检查项目完善/增加删除假删功能
## 1.0.549_xjbt更新日志 
- 体检端
  - 检查项目完善
## 1.0.548_xjbt更新日志 
- 体检端
  - 收费项目bug
## 1.0.547_xjbt更新日志 
- 体检端
  - 检查费用管理查询
  - 体检登记查询
## 1.0.546_xjbt更新日志 
- 体检端
  - bugfix
  - 体检登记危害因素搜索
  - 体检报告审核通过生成suspect
## 1.0.545_xjbt更新日志 
- 体检端
  - 体检查询缺陷修改
## 1.0.544_xjbt更新日志 
- 体检端
  - 体检报告查询缺陷修改
## 1.0.543_xjbt更新日志 
- 体检端
  - 体检预约预警
## 1.0.542_xjbt更新日志 
- 体检端
  - 体检预约预警
## 1.0.541_xjbt更新日志 
- 体检端
  - 体检预警和统计
## 1.0.540_xjbt更新日志 
- 体检端
  - 计算类项目
  - 功能调整
## 1.0.539_xjbt更新日志
- 体检端
  - 软测问题汇总修改
## 1.0.538_xjbt更新日志
- 体检端
  - update 体检机构名称2
## 1.0.537_xjbt更新日志
- 体检端
  - update 体检机构名称
## 1.0.536_xjbt更新日志
- 体检端
  - oauth更新用户信息
## 1.0.535_xjbt更新日志
- 体检端
  - 报告审核按钮
## 1.0.534_xjbt更新日志
- 体检端
  - 调整xjbt分支默认区域编码
## 1.0.533_xjbt更新日志
- 体检端 
 - 过程管理
 - 报告管理
## 1.0.532_xjbt更新日志
- 体检端
 - 体检接口完善
## 1.0.531_xjbt更新日志
- 体检端
 - 解决接口not found问题
## 1.0.530_xjbt更新日志
- 体检端
  - 用人单位维护调整：先调整为 必须在企业端已注册的企业(adminorg中)才能新建
  - 体检合同：可选企业列表为当前机构维护的客户企业列表
  - 预约处理：体检端同意体检人员预约后为其生成体检编号和体检登记表
  - 体检登记：允许未预约人员直接登记
## 1.0.528_xjbt更新日志
- 体检端
  - 添加体检数据
## 1.0.529_xjbt更新日志
- 体检端
  - 适配兵团国密改造
## 1.0.528_xjbt更新日志
- 体检端
  - 添加体检数据
## 1.0.527_xjbt更新日志
- 体检端
  - 调整oidc,创建门户机构
## 1.0.526_xjbt更新日志
- 体检端
  - 职业健康检查业务管理
## 1.0.524_xjbt更新日志
- 体检端
  - 职业健康检查过程管理/职业健康检查设置
## 1.0.523_xjbt更新日志
- 体检端
  - 体检机构备案文件上传问题
## 1.0.522_xjbt更新日志
- 体检端
  - tj增加oidc登录
## 1.0.521_xjbt更新日志
- 体检端
  - tj增加oidc登录
## 1.0.520_xjbt更新日志
- 体检端
  - 职业健康管理菜单完善
## 1.0.519_xjbt更新日志
- 体检端
  - 合同BUG修改、疑似职业病上报
## 1.0.518_xjbt更新日志
- 体检端
  - 合同列表调整优化

## 1.0.517_xjbt更新日志
- 功能新增
  - 调整xjbt navbar样式，增加主题配置文件,根据不同分支

## 1.0.516_xjbt更新日志
- 体检端
  - 机构备案调整

## 1.0.510_xjbt更新日志
- 体检端
  - 体检端合同修改

## 1.0.509_xjbt更新日志
- 功能新增
  - 体检机构备案

## 1.0.506_xjbt更新日志
- 配置修改
  - 增加多平台镜像打包

## 1.0.505_xjbt更新日志
- 功能新增
  - 新疆config、登录页
  - navbar的title和logo显示使用congif的配置
## 1.0.515更新日志

- 功能新增
  - 镜像漏洞

## 1.0.512更新日志

- 功能新增
  - 云南config

## 1.0.507更新日志

- 功能新增
  - 新增数据库敏感数据加密，访问控制完整性（角色权限人员hmac），重要数据存储机密性，重要数据存储完整性（操作日志）
  - 新增openssl，sm4，福州sm4加密，sm3，福州sm3算法hmac计算
  - 密码存储校验改为sm3，福州sm3
  - 可切换当前加密算法
  - 模糊查询加密数据
  - 封装通用参数解密返回明文数据
  - 修改数据库查询封装方法适用于加密数据查询
  - 增加通用插件，适用于其他需要加密的表和字段和查询逻辑

## 1.0.505更新日志

- 功能新增
  - 呼市config、登录页
  - navbar的title和logo显示使用congif的配置

## 1.0.477更新日志

- 功能修复
  - 用人单位无法编辑信息

## 1.0.477更新日志

- 功能新增
  - 体检预约选择时间段

## 1.0.476更新日志

- 功能修改
  - 19开头的手机号无法注册

## 1.0.475更新日志

- 功能调整

  - 职业病诊断 - 添加诊断信息：车间岗位搜索

## 1.0.474更新日志

- 功能调整

  - 职业病诊断 - 添加诊断信息：车间岗位搜索

## 1.0.474更新日志

- doc

  - 修改sxcc地址

## 1.0.473更新日志

- bugFix

  - 修复建德市第二人民医院体检报告解析失败

## 1.0.472更新日志

- 功能新增
- 集成问卷系统

## 1.0.471更新日志

- 功能调整

  - 修改相应检查项目查询保存部分逻辑

## 1.0.470更新日志

- bugFix
  - 修复体检项目中异常指标医学建议意见字段无法保存的问题

## 1.0.468更新日志

- bugFix

  - 检查项目表头筛选

## 1.0.467更新日志

- 功能新增

  - 体检项目管理
  - 体检预约管理

## 1.0.466更新日志

- 功能新增

  - 体检项目管理
  - 体检预约管理

## 1.0.466更新日志

- bugFix

  - 修复体检列表只显示一页的问题

## 1.0.465更新日志

- bugFix

  - 修复杭州预警短信发送失败的问题

## 1.0.464更新日志

- 功能修复

  - 优化高德地图获取详细地址的方式

## 1.0.463更新日志

- 样式调整

  - 登录页 表单验证不通过时，展示错误信息文字

## 1.0.461更新日志

- 功能修复

  - 优化解析规则，修复分页错误

## 1.0.459,1.0.460更新日志

- 配置更新

  - unittest报错

## 1.0.458更新日志

- 漏洞修复

## 1.0.457更新日志

- 配置更新

  - 新增卫康职卫云站点配置文件

## 1.0.456更新日志

- 配置更新

  - 在Dockerfile中清理npm代理配置

## 1.0.453更新日志

- 功能修复

  - 手机号为原手机号时，发送验证码提示错误

## 1.0.453更新日志

- 配置更新

  - 心跳添加状态码，修改危害因素导入后的样式

## 1.0.452更新日志

- 配置更新

  - 尝试修复OperationPlan超时问题

## 1.0.450, 451更新日志

- 配置更新
  - 添加北元by、山西焦煤sxcc及内网jkqy配置文件
  - 去掉展示的区域限制

## 1.0.449更新日志

- 功能修改
  - 体检机构危害因素功能调整

## 1.0.448更新日志

- 配置更新
  - 修改sit测试环境db uri

## 1.0.447 更新日志

- 功能修复
- 修复体检机构危害因素功能

## 1.0.446 更新日志

- 功能新增
- 将体检机构的危害因素与系统的相匹配

## 1.0.445 更新日志

- 配置修改
- zyws.cn站点迁移至k8s集群

## 1.0.442,443,444更新日志

- 功能调整
  - 除杭州外体检报告身份证改为必填
  - 更换node-sass 为sass

## 1.0.441 更新日志

- 配置修改
- 杭州站点迁移至k8s集群

## 1.0.440 更新日志

- 功能调整
- 设置异常指标非必填。增加适配萧开医院的体检编号适配

## 1.0.439 更新日志

- 功能修复
- 修复解析用人单位地址错误的问题

## 1.0.438 更新日志

- 功能修复
- 修复model索引问题

# 监管端 版本 更新日志

## 1.0.437 更新日志

- 功能调整
- 添加的员工状态值为false，用户名和手机号不能和库里的重复

## 1.0.436 更新日志

- 修改K8S配置，日志不在单独输出至日志文件，由Loki自动收集

## 1.0.435 更新日志

- 功能修复
- 修复修改密码时的方法调用不存在的service

## 1.0.434 更新日志

- 功能修复
- 修复体检端用人单位管理报服务500错误

## 1.0.433 更新日志

- 功能新增
- 接收劳动者端发送的体检预约

## 1.0.430 更新日志

- 功能修复
- 员工页面密码显示问题

## 1.0.429 更新日志

- 功能新增
  - 增加系统运行时间

## 1.0.427 更新日志

- 功能修复
  - 人员信息头像显示失败

## 1.0.426 更新日志

- 功能修改
  - 更换算数验证码的算法

## 1.0.425 更新日志

- 功能新增
  - 密码有效期过期后重置
- bug修复
- 修复体检报告识别报错。修复体检地点抓取失败

## 1.0.424 更新日志

- 功能修改
- 更改验证码为算术验证码

## 1.0.422 更新日志

- 功能修改
- 删除多余标记

## 1.0.421 更新日志

- 功能新增
- 添加人员信息模块

## 1.0.420 更新日志

- 功能新增
  - 营业执照登录密码加密传输

## 1.0.419 更新日志

- 功能新增
  - 注册页面增加动态验证码，修改密码校验规范，密码加密传输，修复页脚信息显示

## 1.0.418 更新日志

- 功能修改
  - 修改密码校验规范更改

## 1.0.417 更新日志

- 功能修复
- 尝试解决体检项目导出时报错

## 1.0.416 更新日志

- 功能修复
- 服务端错误导致职业健康检查无法导出

## 1.0.415 更新日志

- 功能修复
- 尝试修复规定时间内无操作未退出

## 1.0.414 更新日志

- 配置修改
- 福州备案通过，去掉端口号5443

## 1.0.413 更新日志

- 功能新增
  - 前端添加密码修改的校验

## 1.0.412 更新日志

-功能修复

- 修复手机登录秒退问题

## 1.0.411 更新日志

-功能修复

- 修复手机登录，首页图形码

## 1.0.410 更新日志

-功能新增

- 超时30min没有操作自动退出登录

## 1.0.409 更新日志

- 功能新增
- 更改密码复杂度策略
- 增加登录失败锁定账户功能
- 增加密码有效期;

## 1.0.408 更新日志

- 配置修改
- 杏花岭正式域名xhlzyws.cn

## 1.0.407 更新日志

-功能新增

- 登录增加图形验证码

## 1.0.406 更新日志

-功能新增

- 添加法律法规列表

## 1.0.405 更新日志

- 福建体检报告解析增加身份证解析规则, 增加适配规则

## 1.0.404 更新日志

- 功能新增
- 密码加密阐述

## 1.0.403 更新日志

- 配置修改
- 杏花岭区暂用子域名xhl.zyws.cn

## 1.0.402 更新日志

- bug修复
- 处理从企业端报送的诊断项目时，区域地址无法正常选择

## 1.0.401 更新日志

- 配置修改
- 增加杏花岭配置文件

## 1.0.400 更新日志

- bugFix
- 修复体检报告批量上报的退回状态同步修改

## 1.0.399 更新日志

- bugFix
- 完善其余分支首页页脚信息显示

## 1.0.398 更新日志

- bugFix
- 尝试修复首页页脚信息显示

## 1.0.396,397更新日志

- 配置更新
- demo站点url fzzyws.cn
- 产线默认输出日志到console
- 反向代理配置

## 1.0.395 更新日志

- 功能新增
- 增加demo站配置文件

## 1.0.393 更新日志

- bugFix
- 职业健康检查的表格滚动条修复

## 1.0.392 更新日志

- bugFix
- 职业健康检查的表格左侧固定列遮住底部水平滑块

## 1.0.391 更新日志

- bugFix
- 优化解析报告时间解析错误的情况，危害因素解析增加接触危害因素名称的匹配规则，设置autocomplete false关闭自动填充

## 1.0.390 更新日志

- bugFix
- 修复查询语法$i改i

## 1.0.389 更新日志

- 配置更改
- package.js中依赖包添加egg_alinode
- ci去掉build步骤，test中已包含

## 1.0.388 更新日志

- bugFix
- 职业健康检查的表格两侧固定列与底部不对齐

## 1.0.387 更新日志

- 配置更新
- Dockerfile移掉verionLog.md
- 部分插件版本更新

## 1.0.386 更新日志

- 功能新增
- 内网支持网络代理调用外部API
- CI/Docker环境更新为node:18-alpine

## 1.0.385 更新日志

- 功能调整 xxn
  - 用人单位管理-新增：关键字搜索企业时对企业名做去重处理
  - 首页-设置中名字最少字符改为2个。
  - 职业健康检查-项目详情：报告文件增加预览功能

## 1.0.384 更新日志

- 配置修改
  - 福州域名改为fzcdc.org.cn

## 1.0.383 更新日志

- bugFix xxn
  - 设置页面：解决点击保存报错、点击获取验证码有时候没反应以及验证码可以复用的问题

## 1.0.382 更新日志

- bugFix xxn
  - 职业病诊断诊断书的预览功能修复

## 1.0.381 更新日志

- 功能调整 xxn
  - 已解除的预警，预警内容变更而且等级小于原预警时才会更新预警状态
  - 职业病诊断：增加诊断书的预览功能
  - 新增preview公共插件，word预览改为preview

## 1.0.380 更新日志

- 适配福州体检报告

## 1.0.379 更新日志

- 功能新增
- 系统添加水印

## 1.0.378 更新日志

- 功能调整 xxn
  - 已解除的预警，预警内容变更但是等级没变时不更新预警状态

## 1.0.377 更新日志

- bugFix xxn
  - 解决已解除的预警，预警内容变更时没有更新预警状态的问题

## 1.0.376 更新日志

- 功能调整 xxn
  - 首页-设置：按照新设计重构首页设置用户信息页面和功能，如果新的手机号码已经注册，但是未绑定机构，将该号码与机构进行绑定，覆盖原手机号码
  - 预警：已解除的预警若修改项目信息未造成预警内容变更则不新建预警

## 1.0.375 更新日志

- 功能调整 xxn
  - 杭州分支：创建/编辑/导入/上报项目时，增加项目工作场所是否全部为杭州市的校验

## 1.0.374 更新日志

- 新增功能 xxn
  - 用人单位管理：1、新增企业时，增加工作场所，默认与注册地址同步；2、查询企业时默认先查系统中已注册的企业，其次才是工商注册信息；3、注册地址、工作场所地址与系统中企业表中的地址保持一致，保存时，地址和企业表中的地址不一致时，增加弹框提醒；4、联系人和联系电话与机构进行绑定，系统中第一次创建企业时，联系人与联系电话同步到企业表联系人中

## 1.0.373 更新日志

- 新增功能 xxn
  - 杭州分支：创建/编辑/导入/上报项目时，增加项目工作场所是否为杭州市校验
  - heartbeat接口中新增当前版本号的获取和输出

## 1.0.372 更新日志

- 修复体检项目存在-()搜索不出的问题

## 1.0.371 更新日志

- 项目创建失败但是生成预警

## 1.0.370 更新日志

- 功能调整 xxn
  - 用人单位管理：企业信息展示中添加机构企业联系人和联系电话的展示
- 缺陷修复
  - 用人单位管理：修复企业工作场所显示不全的问题

## 1.0.369 更新日志

- 功能调整 xxn
  - 职业病诊断：诊断项目中增加项目联系人、联系方式和项目地址三个字段的增删改查，同时设置默认值为企业联系人、联系方式和工作场所
  - 诊断预警同步职业病诊断的修改做相应的调整
  - 用人单位管理：增加编辑用人单位信息功能，修改企业表绑定体检机构的数据类型为对象，新增机构企业联系人和联系方式，并在企业信息的增删改查时做同步相关处理

## 1.0.368 更新日志

- 配置更改 xjx
  --修改体检端登录cookie名称
  --修改.gitlab-ci.yml复用上次build的cache

## 1.0.367 更新日志

- 功能调整 xxn
  - 职业病诊断：1、新增职业病人员时同时创建employee；2、添加劳动者对应的企'业信息可以创建和修改企业信息；3、诊断证明书改为必填项
  - 新增体检项目时联系人作为必填项，联系方式按照手机号码进行校验

## 1.0.366 更新日志

- bugFix xxn
  - 解决操作日志update报错
  - 解决删除职业病时，没有更新企业表职业病数量的问题
- 功能调整
  - 手动新增体检项目时，联系人改为必填项，联系方式按照手机号码进行校验

## 1.0.365 更新日志

- 配置更改 xjx
  - 合肥站点采用集群部署
  - Fix log path

## 1.0.364 更新日志

- 配置更改 xjx
  - 集群数据库名称和日志路径采用环境变量
  - 数据库名称和认证采用参数方式

## 1.0.363 更新日志

- 配置更改 xjx
  - 连接集群内数据库

## 1.0.362 更新日志

- bugFix
  - 解决删除体检项目时，因为找不到报告文件和其他文件而报错的问题
  - 对siteFunc中的deleteFile做容错处理

## 1.0.361 更新日志

- 新增功能
  - 增加查看体检接口日志

## 1.0.360 更新日志

- bugFix
  - 解决登录页控制台报错：style/login.css文件类型错误
  - 新增/删除/修改 体检项目或职业病信息之后, 同步更新企业表healcheckInfo字段中的统计数据

## 1.0.359 更新日志

- 配置更改
  - 福建静态文件目录采用统一的/opt/public,由容器配置映射至主机/opt/fujian

## 1.0.358 更新日志

- bugFix
  - 解决首页已完成项目点击跳转页面错误以及项目列表接收参数的问题

## 1.0.357 更新日志

- 配置更改 xjx
  - package.json添加k8s的启动入口以支持Kubernetes集群，通过容器环境EGG_SERVER_ENV决定加载哪个配置文件
  - 修改config.fj.js, 通过K8S服务名称访问iservice
  - 福建正式域名 fjzyws.cn
